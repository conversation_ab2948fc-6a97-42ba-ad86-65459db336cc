<template>
  <!-- 通用奖励弹窗：反水奖励、注册奖励等 -->
  <ZPopOverlay :show="showDialog">
    <div class="wrap" :style="{ backgroundImage: `url(${lightBgImg})` }">
      <!-- 顶部礼盒图片 -->
      <img class="head-gift" :src="giftImg" alt="" />
      <!-- 内容外框-border显示 -->
      <div class="content-border">
        <!-- 内容 -->
        <div class="content">
          <!-- 标题 -->
          <img class="head-title" :src="congratulationsImg" alt="" />
          <div>
            <!-- 提示文本 -->
            <div class="tips" :style="{ 'text-align': isRegisterBonus ? 'center' : 'left' }">
              {{ promotionText }}
            </div>
            <!-- 金额 -->
            <div class="bonus-wrap">
              <IconCoin class="icon" :size="40" />
              <span class="bonus">{{ formattedBonusAmount }}</span>
            </div>
          </div>
          <div class="btn" ref="confirmBtnRef">
            <GradientButton
              background-gradient="linear-gradient(180deg, #FF1E35 20.59%, #FF916C 94.85%)"
              border-gradient="#FFDFBF"
              :showLight="true"
              @click="handleClick"
              >Done</GradientButton
            >
          </div>
          <div class="date" v-if="rewardDateText">{{ rewardDateText }}</div>
        </div>
      </div>
    </div>
  </ZPopOverlay>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { storeToRefs } from "pinia";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { AutoPopMgr } from "@/utils/AutoPopMgr";
import { postActivityBonusReceive, postAdjustmentBonusRecordReceive } from "@/api/activity";
import { useGlobalStore } from "@/stores/global";
import { getToken } from "@/utils/auth";
// 导入图片资源，确保与预加载使用相同的路径
import giftImg from "@/assets/images/popDialog/activityBonus-headGift.png";
import lightBgImg from "@/assets/images/popDialog/activityBonus-light.png";
import congratulationsImg from "@/assets/images/popDialog/activityBonus-congratulations.png";

const globalStore = useGlobalStore();
const autoPopMgrStore = useAutoPopMgrStore();
const { showBonusTip, currentBonusData, shouldShowBonusTip } = storeToRefs(autoPopMgrStore);
const emit = defineEmits(["start-coin-animation"]);
const confirmBtnRef = ref<HTMLElement | null>(null);

// 标记是否正在发送领奖请求
const isSending = ref(false);

// 使用统一的数据源
const showDialog = ref(false);
const bonusData = computed(() => currentBonusData.value);

// 监听 shouldShowBonusTip 的变化来控制弹窗显示
watch(
  shouldShowBonusTip,
  (newValue) => {
    if (newValue && bonusData.value) {
      showDialog.value = true;
    } else {
      showDialog.value = false;
    }
  },
  { immediate: true }
);

// 判断当前显示的弹窗类型
const isRegisterBonus = computed(() => bonusData.value?.type === "register");
const isActivityBonus = computed(() => bonusData.value?.type === "activity");

// 显示文本
const promotionText = computed(() => {
  if (!bonusData.value) return "";

  if (isRegisterBonus.value) {
    return bonusData.value.title;
  } else {
    const title = bonusData.value.title;
    return title ? `You received a bonus in ${title} promotion: ` : "";
  }
});

// 奖励日期文本
const rewardDateText = computed(() => {
  if (!bonusData.value || isRegisterBonus.value) return "";
  return bonusData.value.date ? `Reward date: ${bonusData.value.date}` : "";
});

// 格式化金额
const formattedBonusAmount = computed(() => {
  if (!bonusData.value) return "--";
  const amount = bonusData.value.amount || 0;
  return amount >= 0 ? amount.toLocaleString() : "--";
});

// 统一的点击处理函数
const handleClick = async () => {
  if (!bonusData.value) return;

  if (isRegisterBonus.value) {
    // 注册奖励逻辑
    emit("start-coin-animation", confirmBtnRef.value);
    // 防止重复弹框
    globalStore.updateRegisterAward({ type: 0 });
    setTimeout(() => {
      AutoPopMgr.destroyCurrentPopup();
    }, 2000);
  } else if (isActivityBonus.value) {
    // 活动奖励逻辑
    if (!getToken()) return;
    if (isSending.value) return;
    isSending.value = true;

    const params = {
      type: bonusData.value.bonusType || "",
      id: bonusData.value.id,
    };

    // 根据 activity_name 判断使用哪一个 API
    const apiFn = bonusData.value.activity_name
      ? postAdjustmentBonusRecordReceive
      : postActivityBonusReceive;

    try {
      await apiFn(params);

      // 先关闭当前弹窗
      showDialog.value = false;
      emit("start-coin-animation", confirmBtnRef.value);

      // 延迟移除数据和处理下一个弹窗，避免闪现
      setTimeout(() => {
        // 移除本次奖励
        autoPopMgrStore.popActivityBonus?.shift();

        // 检查是否还有其他奖励需要显示
        if (autoPopMgrStore.popActivityBonus.length > 0) {
          // 延迟一点时间再显示下一个弹窗，确保平滑过渡
          showDialog.value = true;
        } else {
          AutoPopMgr.destroyCurrentPopup();
        }
      }, 2000);
    } catch (error) {
      // 出错时先关闭弹窗
      showDialog.value = false;

      setTimeout(() => {
        // 移除当前奖励
        autoPopMgrStore.popActivityBonus?.shift();

        if (autoPopMgrStore.popActivityBonus.length === 0) {
          AutoPopMgr.destroyCurrentPopup();
        } else {
          // 如果还有其他奖励，延迟显示下一个
          setTimeout(() => {
            showDialog.value = true;
          }, 300);
        }
      }, 2000);
    } finally {
      isSending.value = false;
    }
  }
};

// 由于数据已经在 store 的 getter 中统一处理，不再需要复杂的初始化逻辑

defineExpose({
  confirmBtnRef,
});
</script>

<style lang="scss" scoped>
.wrap {
  width: 100%;
  text-align: center;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
  position: relative;
  font-family: "Inter";
  .head-gift {
    width: 160px;
    height: auto;
    margin: 0 auto;
    position: absolute;
    top: 95px;
    left: 30%;
  }
  .head-title {
    width: 220px;
    height: auto;
    margin: 0 auto 20px;
  }
  .content-border {
    background: #fff6c6;
    padding: 2px;
    margin: 150px 24px 90px;
    // min-height: 300px;
    border-radius: 33px;
    .content {
      border-radius: 33px;
      padding: 73px 21px 25px;
      background: linear-gradient(180deg, #ffd0d0cb 0%, #fff 30%);
      height: 100%;
      // min-height: 298px;
      width: 100%;
    }
  }
  .tips {
    color: #222;
    text-align: left;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px;
  }
  .bonus-wrap {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    margin-top: 16px;
    line-height: 1;
    .bonus {
      color: #222;
      font-family: "D-DIN";
      font-size: 36px;
      font-style: normal;
      font-weight: 700;
    }
  }

  .btn {
    margin-top: 36px;
  }
  .date {
    margin-top: 8px;
    color: #222;
    text-align: center;
    font-size: 16px;
    font-weight: 400;
    line-height: normal;
  }
}
</style>
